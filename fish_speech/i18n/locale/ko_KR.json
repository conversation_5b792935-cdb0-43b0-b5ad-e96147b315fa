{"16-mixed is recommended for 10+ series GPU": "10+ 시리즈 GPU에는 16-mixed를 권장합니다.", "5 to 10 seconds of reference audio, useful for specifying speaker.": "화자를 특정하는 데 유의미한 5~10초의 길이의 참조 오디오 데이터.", "A text-to-speech model based on VQ-GAN and Llama developed by [Fish Audio](https://fish.audio).": "[Fish Audio](https://fish.audio)에서 개발한 VQ-GAN 및 Llama 기반의 텍스트 음성 변환 모델.", "Accumulate Gradient Batches": "그라디언트 배치 누적", "Add to Processing Area": "처리 영역에 추가", "Added path successfully!": "경로가 성공적으로 추가되었습니다!", "Advanced Config": "고급 설정", "Base LLAMA Model": "기본 LLAMA 모델", "Batch Inference": "배치 추론", "Batch Size": "배치 크기", "Changing with the Model Path": "모델 경로에 따라 변경 중", "Chinese": "중국어", "Compile Model": "모델 컴파일", "Compile the model can significantly reduce the inference time, but will increase cold start time": "모델을 컴파일하면 추론 시간이 크게 줄어들지만, 초기 시작 시간이 길어집니다.", "Copy": "복사", "Data Preprocessing": "데이터 전처리", "Data Preprocessing Path": "데이터 전처리 경로", "Data Source": "데이터 소스", "Decoder Model Config": "디코더 모델 설정", "Decoder Model Path": "디코더 모델 경로", "Disabled": "비활성화 됨", "Enable Reference Audio": "참고 음성 활성화", "English": "영어", "Error Message": "오류 메시지", "File Preprocessing": "파일 전처리", "Generate": "생성", "Generated Audio": "생성된 오디오", "If there is no corresponding text for the audio, apply ASR for assistance, support .txt or .lab format": "오디오애 대응하는 텍스트가 없을 경우, ASR을 적용해 지원하며, .txt 또는 .lab 형식을 지원합니다.", "Infer interface is closed": "추론 인터페이스가 닫혔습니다.", "Inference Configuration": "추론 설정", "Inference Server Configuration": "추론 서버 설정", "Inference Server Error": "추론 서버 오류", "Inferring interface is launched at {}": "추론 인터페이스가 {}에서 시작되었습니다.", "Initial Learning Rate": "초기 학습률", "Input Audio & Source Path for Transcription": "전사할 입력 오디오 및 소스 경로", "Input Text": "입력 텍스트", "Invalid path: {}": "유효하지 않은 경로: {}", "It is recommended to use CUDA, if you have low configuration, use CPU": "CUDA 사용을 권장하며, 낮은 사양일 경우 CPU를 사용하는 것을 권장합니다.", "Iterative Prompt Length, 0 means off": "반복 프롬프트 길이. (0:비활성화)", "Japanese": "일본어", "LLAMA Configuration": "LLAMA 설정", "LLAMA Model Config": "LLAMA 모델 설정", "LLAMA Model Path": "LLAMA 모델 경로", "Labeling Device": "라벨링 장치", "LoRA Model to be merged": "병합할 LoRA 모델", "Maximum Audio Duration": "최대 오디오 길이", "Maximum Length per Sample": "샘플당 최대 길이", "Maximum Training Steps": "최대 학습 단계", "Maximum tokens per batch, 0 means no limit": "배치당 최대 토큰 수(0:제한 없음)", "Merge": "병합", "Merge LoRA": "LoRA 병합", "Merge successfully": "성공적으로 병합 되었습니다.", "Minimum Audio Duration": "최소 오디오 길이", "Model Output Path": "모델 출력 경로", "Model Size": "모델 크기", "Move": "이동", "Move files successfully": "파일이 성공적으로 이동되었습니다.", "No audio generated, please check the input text.": "생성된 오디오가 없습니다. 입력된 텍스트를 확인하세요.", "No selected options": "옵션이 선택되지 않았습니다.", "Number of Workers": "작업자 수", "Open Inference Server": "추론 서버 열기", "Open Labeler WebUI": "라벨러 WebUI 열기", "Open Tensorboard": "Tensorboard 열기", "Opened labeler in browser": "브라우저에서 라벨러가 열렸습니다.", "Optional Label Language": "선택적 라벨 언어", "Optional online ver": "온라인 버전 선택", "Output Path": "출력 경로", "Path error, please check the model file exists in the corresponding path": "경로 오류, 해당 경로에 모델 파일이 있는지 확인하십시오.", "Precision": "정밀도", "Probability of applying Speaker Condition": "화자 조건 적용 확률", "Put your text here.": "여기에 텍스트를 입력하세요.", "Reference Audio": "참고 오디오", "Reference Text": "참고 텍스트", "Related code and weights are released under CC BY-NC-SA 4.0 License.": "관련 코드 및 가중치는 CC BY-NC-SA 4.0 라이선스 하에 배포됩니다.", "Remove Selected Data": "선택한 데이터 제거", "Removed path successfully!": "경로가 성공적으로 제거되었습니다!", "Repetition Penalty": "반복 패널티", "Save model every n steps": "n 단계마다 모델 저장", "Select LLAMA ckpt": "LLAMA ckpt 선택", "Select VITS ckpt": "VITS ckpt 선택", "Select VQGAN ckpt": "VQGAN ckpt 선택", "Select source file processing method": "소스 파일 처리 방법 선택", "Select the model to be trained (Depending on the Tab page you are on)": "학습할 모델 선택(탭 페이지에 따라 다름)", "Selected: {}": "선택됨: {}", "Speaker": "화자", "Speaker is identified by the folder name": "화자는 폴더 이름으로 식별됩니다", "Start Training": "학습 시작", "Streaming Audio": "스트리밍 오디오", "Streaming Generate": "스트리밍 생성", "Tensorboard Host": "Tensorboard 호스트", "Tensorboard Log Path": "Tensorboard 로그 경로", "Tensorboard Port": "Tensorboard 포트", "Tensorboard interface is closed": "Tensorboard 인터페이스가 닫혔습니다", "Tensorboard interface is launched at {}": "Tensorboard 인터페이스가 {}에서 시작되었습니다.", "Text is too long, please keep it under {} characters.": "텍스트가 너무 깁니다. {}자 이하로 입력해주세요.", "The path of the input folder on the left or the filelist. Whether checked or not, it will be used for subsequent training in this list.": "왼쪽의 입력 폴더 경로 또는 파일 목록의 경로. 체크 여부에 관계없이 이 목록에서 후속 학습에 사용됩니다.", "Training Configuration": "학습 설정", "Training Error": "학습 오류", "Training stopped": "학습이 중지되었습니다.", "Type name of the speaker": "화자의 이름을 입력하세요.", "Type the path or select from the dropdown": "경로를 입력하거나 드롭다운에서 선택하세요.", "Use LoRA": "LoRA 사용", "Use LoRA can save GPU memory, but may reduce the quality of the model": "LoRA를 사용하면 GPU 메모리를 절약할 수 있지만, 모델의 품질이 저하될 수 있습니다.", "Use filelist": "파일 목록 사용", "Use large for 10G+ GPU, medium for 5G, small for 2G": "10G+ GPU 환경에선 large, 5G에선 medium, 2G에선 small을 사용할 것을 권장합니다.", "VITS Configuration": "VITS 설정", "VQGAN Configuration": "VQGAN 설정", "Validation Batch Size": "검증 배치 크기", "View the status of the preprocessing folder (use the slider to control the depth of the tree)": "전처리 폴더의 상태를 확인합니다(슬라이더를 사용하여 트리의 깊이를 조절합니다)", "We are not responsible for any misuse of the model, please consider your local laws and regulations before using it.": "모델의 오용에 대해 책임지지 않습니다. 사용하기 전에 현지 법률과 규정을 고려하시길 바랍니다.", "WebUI Host": "WebUI 호스트", "WebUI Port": "WebUI 포트", "Whisper Model": "Whisper 모델", "You can find the source code [here](https://github.com/fishaudio/fish-speech) and models [here](https://huggingface.co/fishaudio/fish-speech-1).": "소스 코드는 [이곳](https://github.com/fishaudio/fish-speech)에서, 모델은 [이곳](https://huggingface.co/fishaudio/fish-speech-1)에서 확인하실 수 있습니다.", "bf16-true is recommended for 30+ series GPU, 16-mixed is recommended for 10+ series GPU": "30+ 시리즈 GPU에는 bf16-true를, 10+ 시리즈 GPU에는 16-mixed를 권장합니다", "latest": "최신", "new": "새로운", "Realtime Transform Text": "실시간 텍스트 변환", "Normalization Result Preview (Currently Only Chinese)": "정규화 결과 미리보기(현재 중국어만 지원)", "Text Normalization": "텍스트 정규화", "Select Example Audio": "예시 오디오 선택"}