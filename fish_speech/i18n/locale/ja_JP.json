{"16-mixed is recommended for 10+ series GPU": "10シリーズ以降のGPUには16-mixedをお勧めします", "5 to 10 seconds of reference audio, useful for specifying speaker.": "話者を指定するのに役立つ、5～10秒のリファレンスオーディオ。", "A text-to-speech model based on VQ-GAN and Llama developed by [Fish Audio](https://fish.audio).": "[Fish Audio](https://fish.audio)が開発したVQ-GANとLlamaに基づくテキスト音声合成モデル。", "Accumulate Gradient Batches": "勾配バッチの累積", "Add to Processing Area": "処理エリアに追加", "Added path successfully!": "パスの追加に成功しました！", "Advanced Config": "詳細設定", "Base LLAMA Model": "基本LLAMAモデル", "Batch Inference": "バッチ推論", "Batch Size": "バッチサイズ", "Changing with the Model Path": "モデルのパスに伴って変化する", "Chinese": "中国語", "Compile Model": "モデルのコンパイル", "Compile the model can significantly reduce the inference time, but will increase cold start time": "モデルをコンパイルすると推論時間を大幅に短縮できますが、コールドスタート時間が長くなります", "Copy": "コピー", "Data Preprocessing": "データ前処理", "Data Preprocessing Path": "データ前処理パス", "Data Source": "データソース", "Decoder Model Config": "デコーダーモデルの構成", "Decoder Model Path": "デコーダーモデルのパス", "Disabled": "無効", "Enable Reference Audio": "リファレンスオーディオを有効にする", "English": "英語", "Error Message": "エラーメッセージ", "File Preprocessing": "文書前处理", "Generate": "生成", "Generated Audio": "生成されたオーディオ", "If there is no corresponding text for the audio, apply ASR for assistance, support .txt or .lab format": "音声に対応するテキストがない場合は、ASRを適用してサポートします。.txtまたは.lab形式をサポートしています", "Infer interface is closed": "推論インターフェースが閉じられています", "Inference Configuration": "推論設定", "Inference Server Configuration": "推論サーバー設定", "Inference Server Error": "推論サーバーエラー", "Inferring interface is launched at {}": "推論インターフェースが{}で起動しました", "Initial Learning Rate": "初期学習率", "Input Audio & Source Path for Transcription": "入力オーディオと文字起こしのソースパス", "Input Text": "入力テキスト", "Invalid path: {}": "無効なパス: {}", "It is recommended to use CUDA, if you have low configuration, use CPU": "CUDAの使用をお勧めします。低い構成の場合はCPUを使用してください", "Iterative Prompt Length, 0 means off": "反復プロンプト長。0はオフを意味します", "Japanese": "日本語", "LLAMA Configuration": "LLAMA設定", "LLAMA Model Config": "LLAMAモデル設定", "LLAMA Model Path": "LLAMAモデルパス", "Labeling Device": "ラベリングデバイス", "LoRA Model to be merged": "マージするLoRAモデル", "Maximum Audio Duration": "最大オーディオの長さ", "Maximum Length per Sample": "サンプルあたりの最大長", "Maximum Training Steps": "最大トレーニングステップ数", "Maximum tokens per batch, 0 means no limit": "バッチあたりの最大トークン数。0は制限なしを意味します", "Merge": "マージ", "Merge LoRA": "LoRAのマージ", "Merge successfully": "マージに成功しました", "Minimum Audio Duration": "最小オーディオの長さ", "Model Output Path": "モデル出力パス", "Model Size": "モデルサイズ", "Move": "移動", "Move files successfully": "ファイルの移動に成功しました", "No audio generated, please check the input text.": "オーディオが生成されていません。入力テキストを確認してください。", "No selected options": "選択されたオプションはありません", "Number of Workers": "ワーカー数", "Open Inference Server": "推論サーバーを開く", "Open Labeler WebUI": "ラベラーWebUIを開く", "Open Tensorboard": "Tensorboardを開く", "Opened labeler in browser": "ブラウザでラベラーを開きました", "Optional Label Language": "オプションのラベル言語", "Optional online ver": "オプションのオンラインバージョン", "Output Path": "出力パス", "Path error, please check the model file exists in the corresponding path": "パスエラー。対応するパスにモデルファイルが存在するか確認してください", "Precision": "精度", "Probability of applying Speaker Condition": "話者条件を適用する確率", "Put your text here.": "ここにテキストを入力してください。", "Reference Audio": "リファレンスオーディオ", "Reference Text": "リファレンステキスト", "Related code and weights are released under CC BY-NC-SA 4.0 License.": "関連コードと重みはCC BY-NC-SA 4.0ライセンスの下でリリースされます。", "Remove Selected Data": "選択したデータを削除", "Removed path successfully!": "パスの削除に成功しました！", "Repetition Penalty": "反復ペナルティ", "Save model every n steps": "nステップごとにモデルを保存", "Select LLAMA ckpt": " LLAMA チェックポイントを選択", "Select VITS ckpt": "VITS チェックポイントを選択", "Select VQGAN ckpt": "VQGAN チェックポイントを選択", "Select source file processing method": "ソースファイルの処理方法を選択", "Select the model to be trained (Depending on the Tab page you are on)": "タブページに応じてトレーニングするモデルを選択してください", "Selected: {}": "選択済み: {}", "Speaker": "話者", "Speaker is identified by the folder name": "話者はフォルダ名で識別されます", "Start Training": "トレーニング開始", "Streaming Audio": "ストリーミングオーディオ", "Streaming Generate": "ストリーミング合成", "Tensorboard Host": "Tensorboardホスト", "Tensorboard Log Path": "Tensorboardログパス", "Tensorboard Port": "Tensorboardポート", "Tensorboard interface is closed": "Tensorboardインターフェースが閉じられています", "Tensorboard interface is launched at {}": "Tensorboardインターフェースが{}で起動されました", "Text is too long, please keep it under {} characters.": "テキストが長すぎます。{}文字以内に抑えてください。", "The path of the input folder on the left or the filelist. Whether checked or not, it will be used for subsequent training in this list.": "左側の入力フォルダまたはファイルリストのパス。チェックの有無にかかわらず、このリストの後続のトレーニングに使用されます。", "Training Configuration": "トレーニング設定", "Training Error": "トレーニングエラー", "Training stopped": "トレーニングが停止しました", "Type name of the speaker": "話者の名前を入力", "Type the path or select from the dropdown": "パスを入力するか、ドロップダウンから選択してください", "Use LoRA": "LoRAを使用", "Use LoRA can save GPU memory, but may reduce the quality of the model": "LoRAを使用するとGPUメモリを節約できますが、モデルの品質が低下する可能性があります", "Use filelist": "ファイルリストを使用", "Use large for 10G+ GPU, medium for 5G, small for 2G": "10G以上のGPUには大、5Gには中、2Gには小を使用してください", "VITS Configuration": "VITS の構成", "VQGAN Configuration": "VQGAN の構成", "Validation Batch Size": "検証バッチサイズ", "View the status of the preprocessing folder (use the slider to control the depth of the tree)": "前処理フォルダの状態を表示（スライダーを使用してツリーの深さを制御）", "We are not responsible for any misuse of the model, please consider your local laws and regulations before using it.": "モデルの誤用については一切責任を負いません。使用する前に、現地の法律と規制を考慮してください。", "WebUI Host": "WebUIホスト", "WebUI Port": "WebUIポート", "Whisper Model": "Whisperモデル", "You can find the source code [here](https://github.com/fishaudio/fish-speech) and models [here](https://huggingface.co/fishaudio/fish-speech-1).": "ソースコードは[こちら](https://github.com/fishaudio/fish-speech)、モデルは[こちら](https://huggingface.co/fishaudio/fish-speech-1)にあります。", "bf16-true is recommended for 30+ series GPU, 16-mixed is recommended for 10+ series GPU": "30シリーズ以降のGPUにはbf16-trueを、10シリーズ以降のGPUには16-mixedをお勧めします", "latest": "最新", "new": "新規", "Realtime Transform Text": "リアルタイム変換テキスト", "Normalization Result Preview (Currently Only Chinese)": "正規化結果プレビュー（現在は中国語のみ）", "Text Normalization": "テキスト正規化", "Select Example Audio": "サンプル音声を選択"}