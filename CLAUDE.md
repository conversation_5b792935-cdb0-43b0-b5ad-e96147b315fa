# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Fish-Speech is an advanced multilingual Text-to-Speech (TTS) system featuring:
- Zero-shot & few-shot voice cloning capabilities
- Support for multiple languages (English, Chinese, Japanese, Korean, French, German, Arabic, Spanish)
- Emotion and tone control markers
- High-quality speech synthesis with low error rates (0.008 WER, 0.004 CER)

The project has been rebranded to **OpenAudio** with two model variants:
- **S1**: Full-featured 4B parameter model
- **S1-mini**: Distilled 0.5B parameter model

## Development Commands

### Environment Management (Using UV)

```bash
# Install dependencies
uv sync --python 3.12

# Add new dependencies
uv add <package_name>

# Run Python scripts with UV
uv run python <script_name>.py

# If dependencies fail (e.g., NumPy version conflicts)
uv sync --reinstall
```

### Model Download

```bash
# Download model weights
huggingface-cli download fishaudio/openaudio-s1-mini --local-dir checkpoints/openaudio-s1-mini
```

### Running Services

```bash
# Start WebUI (default)
uv run python -m tools.run_webui

# Start WebUI with custom settings
uv run python -m tools.run_webui \
    --llama-checkpoint-path "checkpoints/openaudio-s1-mini" \
    --decoder-checkpoint-path "checkpoints/openaudio-s1-mini/codec.pth" \
    --decoder-config-name modded_dac_vq \
    --compile  # For faster inference

# Start HTTP API server
uv run python -m tools.api_server \
    --listen 0.0.0.0:8080 \
    --llama-checkpoint-path "checkpoints/openaudio-s1-mini" \
    --decoder-checkpoint-path "checkpoints/openaudio-s1-mini/codec.pth" \
    --decoder-config-name modded_dac_vq \
    --compile
```

### Command Line Inference

```bash
# Step 1: Extract VQ tokens from reference audio
uv run python fish_speech/models/dac/inference.py \
    -i "ref_audio.wav" \
    --checkpoint-path "checkpoints/openaudio-s1-mini/codec.pth"

# Step 2: Generate semantic tokens from text
uv run python fish_speech/models/text2semantic/inference.py \
    --text "Your text here" \
    --prompt-text "Reference text" \
    --prompt-tokens "fake.npy" \
    --compile

# Step 3: Generate audio from semantic tokens
uv run python fish_speech/models/dac/inference.py \
    -i "codes_0.npy"
```

## Architecture Overview

### Core Modules

- **fish_speech/**: Main package containing all model implementations
  - **models/**: Neural network architectures
    - `text2semantic/`: Text to semantic token conversion (language model)
    - `dac/`: Audio codec for encoding/decoding
  - **inference_engine/**: High-level inference APIs
  - **tokenizer.py**: Text tokenization utilities
  - **content_sequence.py**: Sequence handling for training/inference
  - **text/**: Text processing utilities
  - **i18n/**: Internationalization support

- **tools/**: Application layer and utilities
  - `run_webui.py`: Gradio-based web interface
  - `api_server.py`: HTTP API server using Kui framework
  - `api_client.py`: Client for API testing
  - **webui/**: Web UI components
  - **server/**: API server components
  - **llama/**: Language model utilities
  - **vqgan/**: Legacy VQ-GAN utilities

### Key Design Patterns

1. **Two-stage synthesis pipeline**:
   - Text → Semantic Tokens (via language model)
   - Semantic Tokens → Audio (via audio codec)

2. **Modular architecture**: Each component (tokenizer, language model, codec) can be used independently

3. **Multi-backend support**: CUDA, CPU, and MPS (Apple Silicon) device support

4. **Efficient inference**: Optional torch compilation for ~10x speedup

### Platform-Specific Notes

This installation uses custom wheels for Jetpack 6 (ARM64 architecture):
- PyTorch, TorchVision, TorchAudio wheels from local paths
- Custom Flash Attention wheel for ARM64
- OpenCV Python wheel for ARM64

## Code Style Guidelines

- Follow PEP 8 naming conventions
- Use type hints for function parameters and return values
- Handle exceptions with specific try/except blocks
- Document functions with clear docstrings
- Use UTF-8 encoding for all file operations

## Performance Considerations

- **GPU Memory**: 12GB recommended for fluent inference
- **Compilation**: Use `--compile` flag for ~10x speedup (150 tokens/sec on RTX 4090)
- **Half precision**: Use `--half` flag for GPUs without bf16 support
- **Device selection**: Automatically detects CUDA/MPS availability

## Configuration

Default model paths:
- Language model: `checkpoints/openaudio-s1-mini`
- Audio codec: `checkpoints/openaudio-s1-mini/codec.pth`
- Decoder config: `modded_dac_vq`

WebUI specific:
- Default theme: `light`
- Max audio length controlled by `--max-gradio-length` parameter