name: Close inactive issues
on:
  schedule:
    - cron: "0 0 * * *"

jobs:
  close-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 30
          days-before-issue-close: 14
          stale-issue-label: "stale"
          stale-issue-message: "This issue is stale because it has been open for 30 days with no activity."
          close-issue-message: "This issue was closed because it has been inactive for 14 days since being marked as stale."
          days-before-pr-stale: 30
          days-before-pr-close: 30
          stale-pr-label: "stale"
          stale-pr-message: "This PR is stale because it has been open for 30 days with no activity."
          close-pr-message: "This PR was closed because it has been inactive for 30 days since being marked as stale."
          repo-token: ${{ secrets.GITHUB_TOKEN }}
